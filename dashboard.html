<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Quản Lý</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            padding: 0 20px 30px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #555;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .menu-item:hover {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
            color: #667eea;
        }

        .menu-item.active {
            background: rgba(102, 126, 234, 0.15);
            border-left-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }

        .menu-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .breadcrumb {
            color: #666;
            font-size: 14px;
        }

        .breadcrumb span {
            color: #667eea;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* Form Card */
        .form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 800px;
        }

        .form-header {
            margin-bottom: 30px;
        }

        .form-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .form-subtitle {
            color: #666;
            font-size: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }

        input, select {
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #667eea;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 40px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e1e5e9;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .info-section {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: 600;
            color: #333;
        }

        .info-value {
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-chart-line"></i> Admin Panel</h2>
            </div>
            <nav>
                <a href="#" class="menu-item">
                    <i class="fas fa-chart-pie"></i>
                    Quản lý log
                </a>
                <a href="#" class="menu-item active">
                    <i class="fas fa-users"></i>
                    Quản lý tài khoản
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-user-friends"></i>
                    Quản lý tin nhắn
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-users-cog"></i>
                    Quản lý khách hàng
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-chart-bar"></i>
                    Báo cáo thống kê
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-database"></i>
                    Cài đặt
                </a>
                <a href="#" class="menu-item" style="margin-top: 20px; border-top: 1px solid rgba(0,0,0,0.1); padding-top: 20px;">
                    <i class="fas fa-sign-out-alt"></i>
                    Đăng xuất
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div>
                    <div class="breadcrumb">
                        Quản lý tài khoản / Tài khoản / <span>18503942</span> / Chỉnh sửa
                    </div>
                </div>
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <span>Admin</span>
                </div>
            </div>

            <!-- Form -->
            <div class="form-card">
                <div class="form-header">
                    <h1 class="form-title">18503942 NGUYEN VAN DUY</h1>
                    <div style="display: flex; align-items: center; gap: 10px; margin-top: 15px;">
                        <span style="background: #28a745; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: 600;">Hiệu lực</span>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 25px; color: #856404;">
                    <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                    Yêu cầu người dùng khi đăng nhập
                    <button style="float: right; background: none; border: none; color: #856404; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Tên đăng nhập*</label>
                            <input type="text" id="username" value="18503942" readonly>
                        </div>
                        <div class="form-group">
                            <label for="fullname">Họ và tên*</label>
                            <input type="text" id="fullname" value="NGUYEN VAN DUY">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email*</label>
                            <input type="email" id="email" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="unit">Đơn vị quản lý*</label>
                            <select id="unit">
                                <option value="ha-dong-t/o">HÀ ĐÔNG T/O</option>
                                <option value="other">Khác...</option>
                            </select>
                        </div>
                    </div>

                    <div class="info-section">
                        <div class="info-row">
                            <span class="info-label">Người tạo</span>
                            <span class="info-value"><i class="fas fa-user"></i> NGUYEN NGOC BAO TRAM</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Ngày tạo</span>
                            <span class="info-value"><i class="fas fa-calendar"></i> 15/08/2024 15:39:00</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Người cập nhật gần nhất</span>
                            <span class="info-value"><i class="fas fa-user"></i> NGUYEN NGOC BAO TRAM</span>
                        </div>
                        <div class="info-row" style="margin-bottom: 0;">
                            <span class="info-label">Ngày cập nhật gần nhất</span>
                            <span class="info-value"><i class="fas fa-calendar"></i> 15/08/2024 15:39:00</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Lưu
                        </button>
                        <button type="button" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Hủy
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Toggle menu active state
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Dữ liệu đã được lưu thành công!');
        });
    </script>
</body>
</html>
